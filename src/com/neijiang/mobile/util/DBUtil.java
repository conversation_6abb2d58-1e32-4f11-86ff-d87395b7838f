package com.neijiang.mobile.util;

import org.apache.commons.dbcp2.BasicDataSource;
import java.sql.*;
import java.util.Properties;
import java.io.InputStream;

/**
 * 数据库连接工具类（支持连接池）
 */
public class DBUtil {
    private static String driver;
    private static String url;
    private static String username;
    private static String password;
    private static BasicDataSource dataSource;

    static {
        try {
            // 加载数据库配置
            Properties props = new Properties();
            InputStream is = DBUtil.class.getClassLoader().getResourceAsStream("db.properties");
            if (is != null) {
                props.load(is);
                driver = props.getProperty("db.driver", "com.mysql.cj.jdbc.Driver");
                url = props.getProperty("db.url", "**************************************************************************************************");
                username = props.getProperty("db.username", "root");
                password = props.getProperty("db.password", "123456");
            } else {
                // 默认配置
                driver = "com.mysql.cj.jdbc.Driver";
                url = "**************************************************************************************************";
                username = "root";
                password = "123456";
            }

            // 初始化连接池
            initDataSource();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("数据库配置初始化失败", e);
        }
    }

    /**
     * 初始化数据源（连接池）
     */
    private static void initDataSource() {
        try {
            dataSource = new BasicDataSource();
            dataSource.setDriverClassName(driver);
            dataSource.setUrl(url);
            dataSource.setUsername(username);
            dataSource.setPassword(password);

            // 连接池配置
            dataSource.setInitialSize(5);          // 初始连接数
            dataSource.setMaxTotal(20);            // 最大连接数
            dataSource.setMaxIdle(10);             // 最大空闲连接数
            dataSource.setMinIdle(5);              // 最小空闲连接数
            dataSource.setMaxWaitMillis(60000);    // 最大等待时间
            dataSource.setValidationQuery("SELECT 1"); // 验证查询
            dataSource.setTestOnBorrow(true);      // 借用时验证
            dataSource.setTestWhileIdle(true);     // 空闲时验证

        } catch (Exception e) {
            e.printStackTrace();
            // 如果连接池初始化失败，回退到传统方式
            dataSource = null;
            try {
                Class.forName(driver);
            } catch (ClassNotFoundException ex) {
                throw new RuntimeException("数据库驱动加载失败", ex);
            }
        }
    }
    
    /**
     * 获取数据库连接
     */
    public static Connection getConnection() throws SQLException {
        if (dataSource != null) {
            // 使用连接池
            return dataSource.getConnection();
        } else {
            // 回退到传统方式
            return DriverManager.getConnection(url, username, password);
        }
    }
    
    /**
     * 关闭数据库连接
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭PreparedStatement
     */
    public static void closePreparedStatement(PreparedStatement pstmt) {
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭ResultSet
     */
    public static void closeResultSet(ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭所有资源
     */
    public static void closeAll(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        closeResultSet(rs);
        closePreparedStatement(pstmt);
        closeConnection(conn);
    }
    
    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            return false;
        }
    }
}
